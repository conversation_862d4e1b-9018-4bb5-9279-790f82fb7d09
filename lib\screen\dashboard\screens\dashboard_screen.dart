import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../controllers/customer_controller.dart';
import '../../../models/customer_model.dart';
import 'add_customer_screen.dart';
import 'customer_management.dart';

class ProfileCard extends StatelessWidget {
  const ProfileCard({super.key, this.showTitle = true});

  final bool showTitle;

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(18)),
      color: Colors.white,
      child: Padding(
        padding: const EdgeInsets.symmetric(vertical: 18, horizontal: 14),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Company logo and name
            Row(
              children: [
                Image.asset('assets/images/lc-logo.png', height: 36),
                const SizedBox(width: 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        'LC CASH EXPRESS',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Color(0xFF12306E),
                          fontSize: 16,
                        ),
                      ),
                      SizedBox(height: 2),
                      Text(
                        'អិលស៊ី ខេស អិចប្រេស',
                        style: TextStyle(
                          color: Color(0xFF12306E),
                          fontSize: 13,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              height: 4,
              width: double.infinity,
              decoration: const BoxDecoration(
                color: Color(0xFFF6B900),
                borderRadius: BorderRadius.all(Radius.circular(2)),
              ),
            ),
            const SizedBox(height: 12),
            // Officer photo
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFF6B900), width: 2),
                borderRadius: BorderRadius.circular(10),
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.asset(
                  'assets/images/officer.png',
                  height: 80,
                  width: 70,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 10),
            // Khmer and Latin names
            const Text(
              'ឈ្មោះ ចិន សុភក្តី',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                color: Color(0xFF12306E),
              ),
            ),
            const Text(
              'CHEN SOPHEAKDEY',
              style: TextStyle(
                fontWeight: FontWeight.w500,
                fontSize: 15,
                color: Color(0xFF12306E),
              ),
            ),
            const SizedBox(height: 2),
            // Officer ID
            const Text(
              'ID: 0467',
              style: TextStyle(fontSize: 13, color: Colors.blueGrey),
            ),
            const SizedBox(height: 4),
            // Role
            const Text(
              'Portfolio Officer',
              style: TextStyle(color: Color(0xFF12306E), fontSize: 14),
            ),
            const SizedBox(height: 12),
            // Stats
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 18,
              runSpacing: 4,
              children: [
                _buildStatItem(Icons.assignment, '12 Loans'),
                _buildStatItem(Icons.people, '24 Customers'),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: const Color(0xFF12306E), size: 18),
        const SizedBox(width: 4),
        Text(
          text,
          style: const TextStyle(color: Color(0xFF12306E), fontSize: 13),
        ),
      ],
    );
  }
}

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PreferredSize(
        preferredSize: const Size.fromHeight(56), // Compact height
        child: AppBar(
          elevation: 0,
          backgroundColor: const Color(0xFF12306E),
          foregroundColor: Colors.white,
          centerTitle: false,
          titleSpacing: 16,
          title: Row(
            children: [
              // Compact logo
              Container(
                height: 32,
                width: 32,
                decoration: BoxDecoration(
                  color: const Color(0xFFF6B900),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.dashboard_rounded,
                  color: Color(0xFF12306E),
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              // Title with subtitle
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: const [
                    Text(
                      'LC Dashboard',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        height: 1.2,
                      ),
                    ),
                    Text(
                      'Portfolio Management',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.white70,
                        height: 1.0,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: [
            // Notification badge
            Stack(
              children: [
                IconButton(
                  icon: const Icon(Icons.notifications_outlined),
                  iconSize: 22,
                  tooltip: 'Notifications',
                  onPressed: () {
                    // TODO: Show notifications
                  },
                ),
                Positioned(
                  right: 8,
                  top: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF6B900),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 12,
                      minHeight: 12,
                    ),
                    child: const Text(
                      '3',
                      style: TextStyle(
                        color: Color(0xFF12306E),
                        fontSize: 8,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
              ],
            ),
            // Profile menu
            PopupMenuButton<String>(
              icon: Container(
                height: 32,
                width: 32,
                decoration: BoxDecoration(
                  color: const Color(0xFFF6B900).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: const Color(0xFFF6B900),
                    width: 1.5,
                  ),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: Color(0xFFF6B900),
                  size: 18,
                ),
              ),
              tooltip: 'Profile Menu',
              offset: const Offset(0, 45),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              itemBuilder:
                  (context) => [
                    const PopupMenuItem(
                      value: 'profile',
                      child: ListTile(
                        leading: Icon(Icons.person_outline, size: 20),
                        title: Text('Profile'),
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'settings',
                      child: ListTile(
                        leading: Icon(Icons.settings_outlined, size: 20),
                        title: Text('Settings'),
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                    ),
                    const PopupMenuDivider(),
                    const PopupMenuItem(
                      value: 'logout',
                      child: ListTile(
                        leading: Icon(
                          Icons.logout,
                          size: 20,
                          color: Colors.red,
                        ),
                        title: Text(
                          'Logout',
                          style: TextStyle(color: Colors.red),
                        ),
                        contentPadding: EdgeInsets.zero,
                        dense: true,
                      ),
                    ),
                  ],
              onSelected: (value) {
                switch (value) {
                  case 'profile':
                    // TODO: Navigate to profile
                    break;
                  case 'settings':
                    // TODO: Navigate to settings
                    break;
                  case 'logout':
                    // TODO: Handle logout
                    break;
                }
              },
            ),
            const SizedBox(width: 8),
          ],
        ),
      ),
      body: const _DashboardNavigation(),
    );
  }
}

class _DashboardNavigation extends StatefulWidget {
  const _DashboardNavigation();

  @override
  State<_DashboardNavigation> createState() => _DashboardNavigationState();
}

class _DashboardNavigationState extends State<_DashboardNavigation> {
  final CustomerController customerController = Get.put(CustomerController());
  int _selectedIndex = 0;
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController(initialPage: _selectedIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
      _pageController.animateToPage(
        index,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        onPageChanged: (index) {
          setState(() {
            _selectedIndex = index;
          });
        },
        children: const [
          // Home Tab
          SingleChildScrollView(
            padding: EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                ProfileCard(),
                SizedBox(height: 28),
                Text(
                  'Portfolio Officer Dashboard',
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 24),
                // Add more dashboard widgets here
              ],
            ),
          ),
          // List Tab
          CustomerManagement(),
        ],
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          child: BottomNavigationBar(
            currentIndex: _selectedIndex,
            onTap: _onItemTapped,
            backgroundColor: const Color(0xFFF6B900),
            selectedItemColor: const Color(0xFF12306E),
            unselectedItemColor: Colors.blueGrey[700],
            showSelectedLabels: true,
            showUnselectedLabels: true,
            elevation: 8,
            type: BottomNavigationBarType.fixed,
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home_outlined),
                activeIcon: Icon(Icons.home),
                label: 'Home',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.list_outlined),
                activeIcon: Icon(Icons.list),
                label: 'Customers',
              ),
            ],
          ),
        ),
      ),
      floatingActionButton:
          _selectedIndex == 1
              ? FloatingActionButton(
                heroTag: 'dashboard_fab',
                onPressed: () async {
                  final newCustomer = await Get.to(() => AddCustomerScreen());
                  if (newCustomer != null && newCustomer is Customer) {
                    // Use the controller to add the new customer
                    final CustomerController customerController = Get.find();
                    customerController.addCustomer(newCustomer);

                    Get.snackbar(
                      'Success',
                      '${newCustomer.displayName} has been added.',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: Colors.green,
                      colorText: Colors.white,
                    );
                  }
                },
                backgroundColor: const Color(0xFF12306E),
                foregroundColor: Colors.white,
                elevation: 4,
                tooltip: 'Add Customer',
                child: const Icon(Icons.add),
              )
              : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.endFloat,
    );
  }
}
