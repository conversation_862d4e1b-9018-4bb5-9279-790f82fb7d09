import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/customer_controller.dart';
import '../../../models/customer_model.dart';
import '../../../widgets/customer/customer_card.dart';
import 'customer_details_screen.dart';

class CustomerManagement extends StatefulWidget {
  const CustomerManagement({super.key});

  @override
  State<CustomerManagement> createState() => _CustomerManagementState();
}

class _CustomerManagementState extends State<CustomerManagement> {
  final CustomerController customerController = Get.find<CustomerController>();
  final TextEditingController _searchController = TextEditingController();
  String _selectedStatus = 'All';

  // This list will be updated by the filter logic
  List<Customer> _filteredCustomers = [];

  @override
  void initState() {
    super.initState();
    // Initial filter setup
    _filterCustomers();
    // Add listener to search controller to re-filter on text change
    _searchController.addListener(() {
      _filterCustomers();
    });
  }

  void _filterCustomers() {
    final query = _searchController.text.toLowerCase();

    // Start with the full list from the controller
    List<Customer> filtered = customerController.allCustomers.where((customer) {
      final statusMatch = _selectedStatus == 'All' ||
          customer.loanStatus.toString().split('.').last == _selectedStatus;

      if (query.isEmpty) {
        return statusMatch;
      }

      final nameMatch = customer.displayName.toLowerCase().contains(query);
      final phoneMatch = customer.phone.contains(query);
      final nidMatch = customer.nid.contains(query);

      return statusMatch && (nameMatch || phoneMatch || nidMatch);
    }).toList();

    // This needs to be inside the Obx build method to react to changes,
    // but for filtering logic, we can update a local state variable that the ListView.builder uses.
    // To trigger rebuilds correctly with Obx, the logic should be inside it.
    // A simple way is to just assign it here and let Obx handle the rest.
    _filteredCustomers = filtered;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: Obx(
        () {
          // Re-run the filter whenever the master list in the controller changes.
          _filterCustomers();
          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
            // Modern Header Section - Made more compact
            Container(
              padding: const EdgeInsets.fromLTRB(16, 12, 16, 12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFF12306E), Color(0xFF1E4A8C)],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                  Row(
                    children: [
                      const Icon(Icons.people, color: Colors.white, size: 24),
                      const SizedBox(width: 8),
                      const Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Customer Management',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            Text(
                              'Manage your loan portfolio',
                              style: TextStyle(
                                color: Colors.white70,
                                fontSize: 12,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Text(
                          '${_filteredCustomers.length}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 11,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // Enhanced Search Bar - Made more compact
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.1),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                      child: TextField(
                        controller: _searchController,
                        decoration: InputDecoration(
                          hintText: 'Search by phone, name, or NID',
                        hintStyle: TextStyle(color: Colors.grey[500]),
                        prefixIcon: Icon(
                          Icons.search,
                          color: const Color(0xFF12306E),
                          size: 20,
                        ),
                          border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                          ),
                        filled: true,
                        fillColor: Colors.white,
                        contentPadding: const EdgeInsets.symmetric(
                          vertical: 12,
                          horizontal: 12,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
            ),
            // Filter Section - Made more compact
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: Colors.white,
              child: Row(
                children: [
                  Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey[300]!),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton<String>(
                          hint: const Text(
                            'Filter by Status',
                            style: TextStyle(fontSize: 14),
                          ),
                          value: _selectedStatus,
                          isExpanded: true,
                          icon: const Icon(
                            Icons.filter_list,
                            color: Color(0xFF12306E),
                            size: 18,
                          ),
                          onChanged: (val) {
                            setState(() {
                              _selectedStatus = val!;
                            });
                            _filterCustomers();
                          },
                          items: [
                            const DropdownMenuItem<String>(
                              value: 'All',
                              child: Text('All Status'),
                            ),
                            ...LoanStatus.values.map(
                              (status) => DropdownMenuItem(
                                value: status.toString().split('.').last,
                                child: Row(
                                  children: [
                                    Container(
                                      width: 8,
                                      height: 8,
                                      decoration: BoxDecoration(
                                        color: _getStatusColor(status),
                                        shape: BoxShape.circle,
                                      ),
                                    ),
                                    const SizedBox(width: 8),
                                    Text(
                                      status
                                          .toString()
                                          .split('.')
                                          .last
                                          .replaceFirst(
                                            status
                                                .toString()
                                                .split('.')
                                                .last[0],
                                            status
                                                .toString()
                                                .split('.')
                                                .last[0]
                                                .toUpperCase(),
                                          ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    ),
                  ],
                ),
              ),
            // Customer List Section
              Expanded(
                child: _filteredCustomers.isEmpty
                      ? _buildEmptyState()
                      : Container(
                        color: Colors.grey[50],
                        child: ListView.builder(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          itemCount: _filteredCustomers.length,
                          itemBuilder: (context, index) {
                            final customer = _filteredCustomers[index];
                            return Container(
                              margin: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(16),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withValues(alpha: 0.05),
                                    blurRadius: 8,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              child: CustomerCard(
                                customer: customer,
                                onTap: () {
                                  Get.to(
                                    () => CustomerDetailsScreen(
                                      customer: customer,
                                    ),
                                  );
                                },
                              ),
                            );
                          },
                        ),
                      ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.people_outline,
              size: 64,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'No customers found',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              color: Colors.grey[600],
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or filter criteria',
            style: Theme.of(
              context,
            ).textTheme.bodyMedium?.copyWith(color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: () {
              _searchController.clear();
              setState(() {
                _selectedStatus = 'All';
              });
              _filterCustomers();
            },
            icon: const Icon(Icons.refresh),
            label: const Text('Clear Filters'),
            style: ElevatedButton.styleFrom(
              backgroundColor: const Color(0xFF12306E),
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(LoanStatus status) {
    switch (status) {
      case LoanStatus.pending:
        return Colors.orange;
      case LoanStatus.approved:
        return Colors.blue;
      case LoanStatus.disbursed:
        return Colors.green;
      case LoanStatus.completed:
        return Colors.purple;
      case LoanStatus.rejected:
        return Colors.red;
    }
  }
}
